apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ops-foc-fault-replay-simple
  namespace: ops-foc
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, PATCH, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "4096m"
spec:
  rules:
    - host: foc.ccops.bcopstest.com
      http:
        paths:
          - path: /ops-foc/fault-replay
            pathType: Prefix
            backend:
              service:
                name: svc-ops-foc-fault-replay
                port:
                  number: 8080
