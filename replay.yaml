apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ops-foc-fault-replay
  namespace: ops-foc
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "APP-ID,content-type,LCDP-CSRF-TOKEN,LCDP-SIGNATURE,Referer,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,Access-Control-Allow-Origin"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, PATCH, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-expose-headers: "*"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "4096m"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/configuration-snippet: |
      # 根据 HTTP 方法进行内部重定向
      if ($request_method ~ ^(POST|PUT|PATCH|DELETE)$) {
        rewrite ^(.*)$ /write$1 last;
      }
      if ($request_method ~ ^(GET|HEAD|OPTIONS)$) {
        rewrite ^(.*)$ /read$1 last;
      }
spec:
  rules:
    - host: foc.ccops.bcopstest.com
      http:
        paths:
          - path: /read/ops-foc/fault-replay(/|$)(.*)
            backend:
              service:
                name: svc-ops-foc-fault-replay-read
                port:
                  number: 8080
            pathType: ImplementationSpecific
          - path: /write/ops-foc/fault-replay(/|$)(.*)
            backend:
              service:
                name: svc-ops-foc-fault-replay-write
                port:
                  number: 8080
            pathType: ImplementationSpecific
          - path: /ops-foc/fault-replay(/|$)(.*)
            backend:
              service:
                name: svc-ops-foc-fault-replay-read
                port:
                  number: 8080
            pathType: ImplementationSpecific