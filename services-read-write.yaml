apiVersion: v1
kind: Service
metadata:
  name: svc-ops-foc-fault-replay-read
  namespace: ops-foc
  labels:
    app: ops-foc-fault-replay
    tier: read
spec:
  selector:
    app: ops-foc-fault-replay
    # 如果有专门的读实例，可以添加特定标签
    # tier: read
  ports:
    - port: 8080
      targetPort: 8080
      protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: svc-ops-foc-fault-replay-write
  namespace: ops-foc
  labels:
    app: ops-foc-fault-replay
    tier: write
spec:
  selector:
    app: ops-foc-fault-replay
    # 如果有专门的写实例，可以添加特定标签
    # tier: write
  ports:
    - port: 8080
      targetPort: 8080
      protocol: TCP
  type: ClusterIP
