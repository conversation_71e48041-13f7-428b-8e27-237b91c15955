apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ops-foc-fault-replay
  namespace: ops-foc
  annotations:
    kubernetes.io/ingress.class: nginx
    # 启用CORS支持
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, PATCH, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-expose-headers: "*"
    # 设置请求体大小限制
    nginx.ingress.kubernetes.io/proxy-body-size: "4096m"
    # 设置超时时间
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    # 基于HTTP方法的流量分流配置
    nginx.ingress.kubernetes.io/configuration-snippet: |
      # 避免重写循环，只对原始请求进行重写
      if ($uri !~ ^/(read-service|write-service)) {
        # 读操作：GET、HEAD、OPTIONS请求路由到读服务
        if ($request_method ~ ^(GET|HEAD|OPTIONS)$) {
          rewrite ^(.*)$ /read-service$1 last;
        }
        # 写操作：POST、PUT、PATCH、DELETE请求路由到写服务
        if ($request_method ~ ^(POST|PUT|PATCH|DELETE)$) {
          rewrite ^(.*)$ /write-service$1 last;
        }
      }
    # URL重写规则 - 去掉服务前缀
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  rules:
    - host: foc.ccops.bcopstest.com
      http:
        paths:
          # 读服务路径 - 处理GET、HEAD、OPTIONS请求
          - path: /read-service(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: svc-ops-foc-fault-replay-read
                port:
                  number: 8080
          # 写服务路径 - 处理POST、PUT、PATCH、DELETE请求
          - path: /write-service(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: svc-ops-foc-fault-replay-write
                port:
                  number: 8080
          # 默认路径 - 兜底路由到读服务
          - path: /(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: svc-ops-foc-fault-replay-read
                port:
                  number: 8080
