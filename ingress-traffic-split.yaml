apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ops-foc-fault-replay
  namespace: ops-foc
  annotations:
    kubernetes.io/ingress.class: nginx
    # 启用CORS支持
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, PATCH, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-expose-headers: "*"
    # 设置请求体大小限制
    nginx.ingress.kubernetes.io/proxy-body-size: "4096m"
    # 基于HTTP方法的流量分流
    nginx.ingress.kubernetes.io/configuration-snippet: |
      # 写操作重写到写服务路径
      if ($request_method ~ ^(POST|PUT|PATCH|DELETE)$) {
        rewrite ^(.*)$ /write$1 last;
      }
      # 读操作重写到读服务路径
      if ($request_method ~ ^(GET|HEAD|OPTIONS)$) {
        rewrite ^(.*)$ /read$1 last;
      }
    # URL重写规则
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  rules:
    - host: foc.ccops.bcopstest.com
      http:
        paths:
          # 读服务路径
          - path: /read/ops-foc/fault-replay(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: svc-ops-foc-fault-replay-read
                port:
                  number: 8080
          # 写服务路径
          - path: /write/ops-foc/fault-replay(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: svc-ops-foc-fault-replay-write
                port:
                  number: 8080
          # 默认路径 - 兜底到读服务
          - path: /ops-foc/fault-replay(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: svc-ops-foc-fault-replay-read
                port:
                  number: 8080
